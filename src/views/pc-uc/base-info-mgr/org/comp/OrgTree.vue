<template>
  <div :class="$style['wrap']" class="com-box">
    <n-tree
      ref="treeRef"
      :class="$style.tree"
      :data="orgTree"
      :selected-keys="selectedKeys"
      key-field="id"
      label-field="text"
      default-expand-all
      block-line
      @update:selected-keys="handleSelectedKeys"
    />

    <n-button v-if="isSuperAdmin" type="primary" @click="setShowDia(true)">新增分支机构</n-button>

    <!--  dialog  -->
    <com-dialog v-model:show="isShowDia" :width="500" :height="200">
      <template #header>
        <span class="font-bold text-[16px]">新增分支机构</span>
      </template>
      <div class="px-[15px] pt-[20px]">
        <n-form ref="formInstRef" :model="formData" :rules="rules">
          <n-form-item label="机构名称" label-placement="left" require-mark-placement="left" path="orgName">
            <n-input v-model:value="formData.orgName" type="text" maxlength="20" show-count />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-flex :size="[50, 0]" justify="center">
          <n-button @click="handleCancelOrg">取消</n-button>
          <n-button type="primary" @click="handleAddOrg">确定</n-button>
        </n-flex>
      </template>
    </com-dialog>
  </div>
</template>

<script lang="ts" setup>
import ComDialog from '@/components/dialog/ComDialog.vue';
import { $toast } from '@/common/shareContext';
import { ACTION } from '../../../common/constant';
import { comGetOrgTree } from '@/views/pc/common/response';
import { FormInst, TreeInst } from 'naive-ui';
import { computed, onMounted, ref, watch } from 'vue';
import { postSave } from '../fetchData.ts';
import { useState } from '@/common/hooks/useState.ts';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);
const isSuperAdmin = computed(() => useAuthPcStore().isSuperAdmin);

const [isShowDia, setShowDia] = useState(false);
const curOrgCode = ref('');
const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm());
const rules = {
  orgName: { required: true, message: '请输入机构名称', trigger: 'blur' },
};
const { orgTree, updateOrgTree } = comGetOrgTree();

// 树组件引用和选中的节点
const treeRef = ref<TreeInst | null>(null);
const selectedKeys = ref<string[]>([]);

function initForm() {
  return {
    orgName: '',
  };
}

// 获取根节点ID
function getRootNodeId() {
  if (orgTree.value && orgTree.value.length > 0) {
    return orgTree.value[0].id || '';
  }
  return '';
}

function handleSelectedKeys(keys: Array<string>) {
  // 如果反选了节点（keys为空），则自动选择根节点
  if (keys.length === 0) {
    const rootId = getRootNodeId();
    if (rootId) {
      selectedKeys.value = [rootId];
      curOrgCode.value = rootId;
    } else {
      curOrgCode.value = '';
    }
  } else {
    selectedKeys.value = keys;
    curOrgCode.value = keys[0] || '';
  }

  // emit
  doHandle(ACTION.SEARCH);
}

function handleAddOrg() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value);

        postSave(params)
          .then(() => {
            updateOrgTree();
            reset();
            resolve('ok');
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function handleCancelOrg() {
  reset();
}

function reset() {
  formData.value = initForm();
  setShowDia(false);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: { orgCode: curOrgCode.value },
  });
}

// 监听组织树数据变化，初始化选中根节点
watch(
  orgTree,
  (newTree) => {
    if (newTree && newTree.length > 0 && selectedKeys.value.length === 0) {
      const rootId = getRootNodeId();
      if (rootId) {
        selectedKeys.value = [rootId];
        curOrgCode.value = rootId;
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcUcBaseInfoMgrOrgTree' });
</script>

<style module lang="scss">
.wrap {
  width: 280px;
  display: grid;
  grid-template-rows: 1fr 40px;
  row-gap: 5px;

  .tree {
    padding: 24px 24px 0;

    // 覆盖所有可能的树节点背景色状态
    :global(.n-tree .n-tree-node .n-tree-node-content) {
      background-color: transparent !important;

      &:hover {
        background-color: transparent !important;
      }

      &:focus {
        background-color: transparent !important;
        outline: none !important;
      }

      &:active {
        background-color: transparent !important;
      }
    }

    // 只有选中状态才显示背景色
    :global(.n-tree .n-tree-node .n-tree-node-content--selected) {
      background-color: var(--n-node-color-active, rgba(24, 160, 88, 0.16)) !important;

      &:hover {
        background-color: var(--n-node-color-active, rgba(24, 160, 88, 0.16)) !important;
      }

      &:focus {
        background-color: var(--n-node-color-active, rgba(24, 160, 88, 0.16)) !important;
      }
    }

    // 去掉所有可能的轮廓线
    :global(.n-tree *) {
      outline: none !important;
    }

    // 特别处理可能的焦点状态
    :global(.n-tree-node-wrapper) {
      &:focus {
        outline: none !important;
        background-color: transparent !important;
      }
    }

    // 强制重置所有可能的背景色类
    :global([class*='n-tree-node']:not([class*='selected'])) {
      background-color: transparent !important;
    }

    // 重置任何可能的悬停、焦点、激活状态
    :global([class*='n-tree-node']:not([class*='selected']):hover),
    :global([class*='n-tree-node']:not([class*='selected']):focus),
    :global([class*='n-tree-node']:not([class*='selected']):active) {
      background-color: transparent !important;
    }
  }

  .modalBox {
    width: 500px;
    height: 450px;
    background: var(--skin-bg1);
  }
}
</style>

<!-- 全局样式重置，确保去掉树节点的背景色 -->
<style lang="scss">
// 使用更强的选择器来覆盖 naive-ui 的默认样式
.n-tree .n-tree-node .n-tree-node-content {
  background-color: transparent !important;
  background: none !important;
}

.n-tree .n-tree-node .n-tree-node-content:hover:not(.n-tree-node-content--selected) {
  background-color: transparent !important;
  background: none !important;
}

.n-tree .n-tree-node .n-tree-node-content:focus:not(.n-tree-node-content--selected) {
  background-color: transparent !important;
  background: none !important;
  outline: none !important;
}

.n-tree .n-tree-node .n-tree-node-content:active:not(.n-tree-node-content--selected) {
  background-color: transparent !important;
  background: none !important;
}

// 确保选中状态正常显示
.n-tree .n-tree-node .n-tree-node-content--selected {
  background-color: var(--n-node-color-active, rgba(24, 160, 88, 0.16)) !important;
}
</style>
