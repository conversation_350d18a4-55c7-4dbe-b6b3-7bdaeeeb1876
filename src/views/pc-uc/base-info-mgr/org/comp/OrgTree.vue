<template>
  <div :class="$style['wrap']" class="com-box">
    <n-tree
      ref="treeRef"
      :class="$style.tree"
      :data="orgTree"
      :selected-keys="selectedKeys"
      key-field="id"
      label-field="text"
      default-expand-all
      block-line
      @update:selected-keys="handleSelectedKeys"
    />

    <n-button v-if="isSuperAdmin" type="primary" @click="setShowDia(true)">新增分支机构</n-button>

    <!--  dialog  -->
    <com-dialog v-model:show="isShowDia" :width="500" :height="200">
      <template #header>
        <span class="font-bold text-[16px]">新增分支机构</span>
      </template>
      <div class="px-[15px] pt-[20px]">
        <n-form ref="formInstRef" :model="formData" :rules="rules">
          <n-form-item label="机构名称" label-placement="left" require-mark-placement="left" path="orgName">
            <n-input v-model:value="formData.orgName" type="text" maxlength="20" show-count />
          </n-form-item>
        </n-form>
      </div>
      <template #action>
        <n-flex :size="[50, 0]" justify="center">
          <n-button @click="handleCancelOrg">取消</n-button>
          <n-button type="primary" @click="handleAddOrg">确定</n-button>
        </n-flex>
      </template>
    </com-dialog>
  </div>
</template>

<script lang="ts" setup>
import ComDialog from '@/components/dialog/ComDialog.vue';
import { $toast } from '@/common/shareContext';
import { ACTION } from '../../../common/constant';
import { comGetOrgTree } from '@/views/pc/common/response';
import { FormInst, TreeInst } from 'naive-ui';
import { computed, onMounted, ref, watch } from 'vue';
import { postSave } from '../fetchData.ts';
import { useState } from '@/common/hooks/useState.ts';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);
const isSuperAdmin = computed(() => useAuthPcStore().isSuperAdmin);

const [isShowDia, setShowDia] = useState(false);
const curOrgCode = ref('');
const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm());
const rules = {
  orgName: { required: true, message: '请输入机构名称', trigger: 'blur' },
};
const { orgTree, updateOrgTree } = comGetOrgTree();

// 树组件引用和选中的节点
const treeRef = ref<TreeInst | null>(null);
const selectedKeys = ref<string[]>([]);

function initForm() {
  return {
    orgName: '',
  };
}

// 获取根节点ID
function getRootNodeId() {
  if (orgTree.value && orgTree.value.length > 0) {
    return orgTree.value[0].id || '';
  }
  return '';
}

function handleSelectedKeys(keys: Array<string>) {
  // 如果反选了节点（keys为空），则自动选择根节点
  if (keys.length === 0) {
    const rootId = getRootNodeId();
    if (rootId) {
      selectedKeys.value = [rootId];
      curOrgCode.value = rootId;
    } else {
      curOrgCode.value = '';
    }
  } else {
    selectedKeys.value = keys;
    curOrgCode.value = keys[0] || '';
  }

  // emit
  doHandle(ACTION.SEARCH);
}

function handleAddOrg() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value);

        postSave(params)
          .then(() => {
            updateOrgTree();
            reset();
            resolve('ok');
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function handleCancelOrg() {
  reset();
}

function reset() {
  formData.value = initForm();
  setShowDia(false);
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: { orgCode: curOrgCode.value },
  });
}

// 监听组织树数据变化，初始化选中根节点
watch(
  orgTree,
  (newTree) => {
    if (newTree && newTree.length > 0 && selectedKeys.value.length === 0) {
      const rootId = getRootNodeId();
      if (rootId) {
        selectedKeys.value = [rootId];
        curOrgCode.value = rootId;
      }
    }
  },
  { immediate: true }
);

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcUcBaseInfoMgrOrgTree' });
</script>

<style module lang="scss">
.wrap {
  width: 280px;
  display: grid;
  grid-template-rows: 1fr 40px;
  row-gap: 5px;

  .tree {
    padding: 24px 24px 0;
  }

  .modalBox {
    width: 500px;
    height: 450px;
    background: var(--skin-bg1);
  }
}
</style>
