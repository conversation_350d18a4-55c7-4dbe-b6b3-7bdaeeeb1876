import type { IBreadData } from '@/types';
import type { IMenu } from '../menu/type';
import { computed } from 'vue';
import { menuDataList } from '../menu/menu';
import { RouteLocationNormalizedLoaded, useRouter } from 'vue-router';

/**
 * 面包屑导航 composable
 * @returns 包含 breadData 的对象
 */
export function useBread() {
  const router = useRouter();

  const breadData = computed<IBreadData[]>(() => {
    const currentRouteName = router.currentRoute.value.name as string;
    const currentRoute = router.currentRoute.value;

    return generateBreadcrumbFromMenu(currentRouteName, menuDataList, currentRoute);
  });

  return {
    breadData,
  };
}

/**
 * 根据路由名称从菜单结构生成面包屑数据
 * @param routeName 当前路由名称
 * @param menuList 菜单列表
 * @param currentRoute 当前路由对象
 * @returns 面包屑数据数组
 */
function generateBreadcrumbFromMenu(
  routeName: string,
  menuList: IMenu[],
  currentRoute: RouteLocationNormalizedLoaded
): IBreadData[] {
  const path = findMenuPath(routeName, menuList);
  if (!path.length) {
    return [];
  }

  // 转换为面包屑格式
  return path.map((menu, index) => {
    const isLast = index === path.length - 1;
    let breadName = menu.label || '';

    // 如果是当前页面，优先从当前路由的 meta.label 获取名称
    if (isLast && currentRoute.meta?.label) {
      breadName = currentRoute.meta.label;
    }

    const breadItem = {
      name: breadName,
    } as IBreadData;

    // 除了最后一个（当前页面）外，其他都设置为可点击
    if (!isLast && menu.routeName) {
      breadItem.clickable = true;
      breadItem.routeRaw = {
        name: menu.routeName,
      };
    }

    return breadItem;
  });
}

/**
 * 递归查找菜单路径
 * @param routeName 目标路由名称
 * @param menuList 菜单列表
 * @param currentPath 当前路径
 * @returns 从根节点到目标节点的路径
 */
function findMenuPath(routeName: string, menuList: IMenu[], currentPath: IMenu[] = []): IMenu[] {
  for (const menu of menuList) {
    const newPath = [...currentPath, menu];

    // 如果找到匹配的路由名称
    if (menu.routeName === routeName) {
      return newPath;
    }

    // 如果有子菜单，递归查找
    if (menu.children && Array.isArray(menu.children)) {
      const result = findMenuPath(routeName, menu.children as IMenu[], newPath);
      if (result.length > 0) {
        return result;
      }
    }
  }

  return [];
}
